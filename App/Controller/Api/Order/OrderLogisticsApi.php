<?php

namespace App\Controller\Api\Order;

use App\Service\OrderLogisticsService;
use App\Service\OrderAutoConfirmService;
use Generate\Models\Datas\OrderLogisticsModel;
use Generate\Tables\Datas\AddressTable;
use Generate\Tables\Datas\OrderLogisticsTable;
use Generate\Tables\Datas\OrderTable;
use GuzzleHttp\Client;
use Swlib\Controller\AbstractController;
use Swlib\Exception\AppException;
use Protobuf\Common\Success;
use Swlib\Router\Router;
use Protobuf\Datas\OrderLogistics\OrderLogisticsProto;
use Protobuf\Datas\OrderLogistics\OrderLogisticsListsProto;
use Swlib\Table\Db;
use Throwable;


/*
* 订单物流信息表
*/

#[Router(method: 'POST')]
class OrderLogisticsApi extends AbstractController
{
    private OrderLogisticsService $orderLogisticsService;

    public function __construct()
    {
        $this->orderLogisticsService = new OrderLogisticsService();
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '保存订单物流信息表失败')]
    public function save(OrderLogisticsProto $request): Success
    {
        $table = OrderLogisticsModel::request($request);

        if (empty($table->sn)) {
            throw new AppException('请输入订单号');
        }

        if (empty($table->skuId)) {
            throw new AppException('请输入规格ID');
        }
        if (empty($table->userId)) {
            throw new AppException('请输入用户ID');
        }
        if (empty($table->logisticsNumber)) {
            throw new AppException('请输入物流单号');
        }

        if (empty($table->number)) {
            throw new AppException('请输入本次发货数量');
        }

        if (empty($table->num)) {
            throw new AppException('请输入本次包裹发货数量');
        }

        $res = Db::transaction(function () use ($table) {
            // 更新发货数量
            $this->orderLogisticsService->updateReceiveNum($table->sn, $table->skuId, $table->num);
            // 更新订单状态
            $this->orderLogisticsService->updateTableStatus($table->sn);

            return $table->save();
        });

        // 如果发货成功，检查订单是否全部发货完成
        if ($res) {
            $this->checkAndStartAutoConfirmProcess($table->sn);
        }

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '获取订单物流信息表列表数据失败')]
    public function lists(OrderLogisticsProto $request): OrderLogisticsListsProto
    {
        $sn = $request->getSn();
        if (empty($sn)) {
            throw new AppException('请输入订单号');
        }

        list($logisticsEntries, $servicesInfo, $skusInfo, $shopOrdersInfo) = $this->orderLogisticsService->getLogisticsData($sn);

        if (empty($logisticsEntries)) {
            return new OrderLogisticsListsProto();
        }

        $protoLists = $this->orderLogisticsService->buildHierarchicalResponse($logisticsEntries, $servicesInfo, $skusInfo, $shopOrdersInfo);

        $ret = new OrderLogisticsListsProto();
        $ret->setLists($protoLists);
        return $ret;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '查看订单物流信息表详情失败')]
    public function detail(OrderLogisticsProto $request): OrderLogisticsProto
    {
        $logisticsNumber = $request->getLogisticsNumber();
        $skuId = $request->getSkuId();
        if (empty($logisticsNumber) || empty($skuId)) {
            throw new AppException("请输入物流单号");
        }

        // 根据物流单号获取该包裹的所有物流记录
        $orderLogisticsTable = new OrderLogisticsTable();
        $logisticsEntries = $orderLogisticsTable
            ->where([
                OrderLogisticsTable::LOGISTICS_NUMBER => $logisticsNumber,
                OrderLogisticsTable::SKU_ID => $skuId,
            ])
            ->order([OrderLogisticsTable::PRI_KEY => 'desc'])
            ->selectAll();

        if (empty($logisticsEntries)) {
            throw new AppException("未找到该包裹信息");
        }

        $sn = $logisticsEntries[0]->sn; // 获取订单号

        // 使用服务类获取相关数据
        list($servicesInfo, $skusInfo, $shopOrdersInfo) = $this->orderLogisticsService->getRelatedData($orderLogisticsTable, $sn);

        // 构建单个包裹的响应数据
        return $this->orderLogisticsService->buildSinglePackageResponse($logisticsEntries, $servicesInfo, $skusInfo, $shopOrdersInfo);
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '删除订单物流信息表失败')]
    public function delete(OrderLogisticsProto $request): Success
    {
        $id = $request->getId();
        if (empty($id)) {
            throw new AppException("参数错误");
        }

        $res = new OrderLogisticsTable()->where([
            OrderLogisticsTable::ID => $id,
        ])->delete();

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     */
    #[Router(errorTitle: '确认收货失败')]
    public function confirmReceiving(OrderLogisticsProto $request): Success
    {
        $sn = $request->getSn();
        $skuId = $request->getSkuId();
        $logisticsNumber = $request->getLogisticsNumber();

        if (empty($sn)) {
            throw new AppException('请输入订单号');
        }

        if (empty($skuId)) {
            throw new AppException('请输入规格ID');
        }

        if (empty($logisticsNumber)) {
            throw new AppException('请输入物流单号');
        }

        $res = Db::transaction(function () use ($sn, $skuId, $logisticsNumber) {
            // 确认收货
            return $this->orderLogisticsService->confirmReceiving($sn, $skuId, $logisticsNumber);
        });

        $msg = new Success();
        $msg->setSuccess((bool)$res);
        return $msg;
    }

    /**
     * @throws Throwable
     * @throws AppException
     */
    #[Router(errorTitle: '查询物流动态失败')]
    public function getLogisticsDynamics(OrderLogisticsProto $request): OrderLogisticsProto
    {
        $expressNo = $request->getLogisticsNumber();


        $orderLogisticsTable = new OrderLogisticsTable()
            ->where([
                [OrderLogisticsTable::LOGISTICS_NUMBER, '=', $expressNo],
            ])
            ->selectOne();

        if (empty($orderLogisticsTable)) {
            throw new AppException('参数错误');
        }

        if ($orderLogisticsTable->lastTime > 0 && $orderLogisticsTable->lastTime < time() - 3600 || $orderLogisticsTable->status == 'SIGN') {
            return OrderLogisticsModel::formatItem($orderLogisticsTable);
        }

        $addId = new OrderTable()->where([
            OrderTable::SN => $orderLogisticsTable->sn,
        ])->selectField(OrderTable::ADDR_ID);


        $phone = new AddressTable()->where([
            AddressTable::ID => $addId
        ])->selectField(AddressTable::PHONE);
//        $expressNo = 464511044846702;
//        $phone = 18008384037;


        $url = "https://kzexpress.market.alicloudapi.com/api-mall/api/express/query";
        $params = [
            'expressNo' => $expressNo,
            'mobile' => $phone
        ];

        $client = new Client();
        $response = $client->get($url, [
            'headers' => [
                'Authorization' => 'APPCODE eaaae86d994f40d5a38882e28be3fda6'
            ],
            'query' => $params
        ]);

        $res = $response->getBody()->getContents();

        $arr = json_decode($res, true);

        if (empty($arr)) {
            throw new AppException('获取物流信息失败');
        }

        $data = $arr['data'];

        new OrderLogisticsTable()->where([
            OrderLogisticsTable::LOGISTICS_NUMBER => $expressNo,
        ])->update([
            OrderLogisticsTable::LAST_TIME => time(),
            OrderLogisticsTable::NEWS => $data['theLastMessage'],
            OrderLogisticsTable::LAST_DETAIL => json_encode($data, JSON_UNESCAPED_UNICODE),
            OrderLogisticsTable::STATUS => $data['logisticsStatus'],

        ]);


        $orderLogisticsTable = new OrderLogisticsTable()
            ->where([
                [OrderLogisticsTable::LOGISTICS_NUMBER, '=', $expressNo],
            ])
            ->selectOne();
        return OrderLogisticsModel::formatItem($orderLogisticsTable);
    }

    /**
     * 检查订单是否全部发货完成，如果是则启动自动确认收货流程
     *
     * @param string $sn 订单号
     * @return void
     * @throws Throwable
     */
    private function checkAndStartAutoConfirmProcess(string $sn): void
    {
        try {
            // 获取订单发货统计信息
            $autoConfirmService = new OrderAutoConfirmService();
            $shippingStats = $autoConfirmService->getOrderShippingStatistics($sn);

            // 如果订单全部发货完成，启动自动确认收货流程
            if ($shippingStats['is_fully_shipped']) {
                OrderAutoConfirmService::startAutoConfirmProcess($sn);
            }
        } catch (Throwable $e) {
            // 记录错误但不影响主流程
            error_log("启动自动确认收货流程失败 - 订单号: {$sn}, 错误: " . $e->getMessage());
        }
    }

}