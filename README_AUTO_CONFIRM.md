# 订单自动确认收货功能

## 功能概述

在商家发货完成后，系统会自动启动延迟消息队列，定期检查物流状态，并在适当时机自动确认收货。

## 实现方案

### 1. 核心组件

#### LogisticsStatusService - 物流状态查询服务
- `queryLogisticsStatus()` - 查询单个物流单号状态
- `queryOrderLogisticsStatus()` - 查询订单所有物流状态
- `isOrderFullySigned()` - 检查订单是否全部已签收
- `getOrderSignedStatistics()` - 获取订单签收统计

#### OrderAutoConfirmService - 自动确认收货服务
- `checkOrderLogisticsStatus()` - 检查物流状态（延迟消息队列方法）
- `autoConfirmReceiving()` - 自动确认收货（延迟消息队列方法）
- `startAutoConfirmProcess()` - 启动自动确认流程

### 2. 流程设计

```
商家发货 → 检查全部发货 → 启动延迟消息队列
                              ↓
                         3天后查询物流状态
                              ↓
                    ┌─────────────────────┐
                    ↓                     ↓
               已签收(SIGN)           未签收
                    ↓                     ↓
            7天后自动确认收货        1天后重新检查
                                         ↓
                                   (最多检查30天)
```

### 3. 延迟消息队列时间设置

- **初始检查**：发货完成后 3天
- **重新检查**：未签收时每 1天 检查一次
- **自动确认**：已签收后 7天 自动确认收货
- **最大检查次数**：30次（30天）

## 代码修改

### 1. OrderLogisticsApi.php 修改

<augment_code_snippet path="App/Controller/Api/Order/OrderLogisticsApi.php" mode="EXCERPT">
```php
// 在 save 方法中添加
$res = Db::transaction(function () use ($table) {
    // 更新发货数量
    $this->orderLogisticsService->updateReceiveNum($table->sn, $table->skuId, $table->num);
    // 更新订单状态
    $this->orderLogisticsService->updateTableStatus($table->sn);

    return $table->save();
});

// 如果发货成功，检查订单是否全部发货完成
if ($res) {
    $this->checkAndStartAutoConfirmProcess($table->sn);
}
```
</augment_code_snippet>

### 2. 新增服务类

- `App/Service/LogisticsStatusService.php` - 物流状态查询服务
- `App/Service/OrderAutoConfirmService.php` - 自动确认收货服务

## 使用方法

### 1. 自动触发（推荐）

发货操作会自动触发：
```php
$orderLogisticsApi = new OrderLogisticsApi();
$result = $orderLogisticsApi->save($request); // 发货后自动启动流程
```

### 2. 手动启动

```php
// 手动启动自动确认流程
OrderAutoConfirmService::startAutoConfirmProcess($orderSn);
```

### 3. 查询物流状态

```php
// 查询单个物流单号状态
$status = LogisticsStatusService::queryLogisticsStatus($logisticsNumber);

// 查询订单所有物流状态
$statuses = LogisticsStatusService::queryOrderLogisticsStatus($orderSn);

// 检查订单是否全部已签收
$isFullySigned = LogisticsStatusService::isOrderFullySigned($orderSn);
```

## 配置要求

### 1. 消息队列

确保 `MessageQueue` 服务正常运行：
- 定期执行 `MessageQueue::run()`
- 监控队列执行状态
- 设置合适的重试机制

### 2. 物流API

第三方物流查询API配置：
- API密钥：`APPCODE eaaae86d994f40d5a38882e28be3fda6`
- 接口地址：`https://kzexpress.market.alicloudapi.com/api-mall/api/express/query`
- 请求参数：`expressNo`（快递单号）、`mobile`（手机号）

### 3. 日志配置

启用相关日志分类：
```php
Log::save($message, 'order_auto_confirm');
```

## 监控和维护

### 1. 关键指标

- **自动确认成功率**：监控自动确认收货的成功率
- **物流查询失败率**：监控第三方API调用失败率
- **队列积压情况**：监控延迟消息队列的执行情况

### 2. 日志监控

关键日志事件：
- 启动自动确认流程
- 物流状态检查结果
- 自动确认收货成功/失败
- API调用异常

### 3. 异常处理

系统设计了多层异常处理：
- **API异常**：记录日志，延迟重试
- **数据异常**：参数验证，避免无效处理
- **业务异常**：状态检查，防止重复操作

## 测试

### 运行测试

```bash
# 基础功能测试
php test_auto_confirm.php
```

### 测试覆盖

1. **物流状态查询测试**
   - 状态描述映射
   - 参数验证
   - API调用

2. **自动确认流程测试**
   - 消息队列处理
   - 状态检查逻辑
   - 异常处理

3. **边界情况测试**
   - 最大检查次数限制
   - 无效参数处理
   - 重复操作防护

## 注意事项

### 1. 性能考虑

- 物流API有调用频率限制，避免频繁查询
- 使用缓存机制减少重复查询
- 批量处理提高效率

### 2. 数据一致性

- 所有关键操作都在事务中执行
- 异常情况不影响主业务流程
- 状态检查防止重复操作

### 3. 扩展性

- 支持不同的物流服务商
- 可配置的时间间隔
- 灵活的重试策略

## 故障排查

### 1. 自动确认不生效

检查项：
- 消息队列是否正常运行
- 物流API是否可访问
- 订单状态是否正确

### 2. 物流状态查询失败

检查项：
- API密钥是否有效
- 网络连接是否正常
- 请求参数是否正确

### 3. 重复确认收货

检查项：
- 消息队列是否重复执行
- 状态检查逻辑是否正确
- 数据库事务是否正常
